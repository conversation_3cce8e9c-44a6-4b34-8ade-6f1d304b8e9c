"use server";

/**
 * Server Actions para métricas de alunos
 */

import { DashboardActionResult, StudentMetrics, DateRange, MetricWithGrowth } from '../../types/dashboard-types';
import { getAuthenticatedClient } from '../shared/auth-utils';
import { formatPercentage, createMetricWithGrowth } from '../../utils/dashboard-utils';

/**
 * Busca métricas de alunos para um período
 *
 * IMPORTANTE: Considera apenas alunos com memberships para consistência
 * com os gráficos e análise financeira. Alunos sem memberships não
 * geram receita e não são relevantes para métricas de negócio.
 */
export async function getStudentMetrics(
  currentRange: DateRange
): Promise<DashboardActionResult<StudentMetrics>> {
  try {
    const { supabase, tenantId } = await getAuthenticatedClient();

    // Buscar alunos ativos (com matrículas ativas)
    const { data: activeStudentsData } = await supabase
      .from('memberships')
      .select('student_id, created_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'active');

    // Buscar total de estudantes com memberships (para consistência com gráficos)
    const { data: allMembershipsData } = await supabase
      .from('memberships')
      .select('student_id, created_at, status')
      .eq('tenant_id', tenantId);

    // Buscar matrículas canceladas no período
    const { data: canceledMembershipsData } = await supabase
      .from('memberships')
      .select('student_id, canceled_at')
      .eq('tenant_id', tenantId)
      .eq('status', 'canceled')
      .gte('canceled_at', currentRange.startDate.toISOString())
      .lte('canceled_at', currentRange.endDate.toISOString());

    // Buscar novos alunos no período (baseado na data de criação da matrícula)
    const { data: newStudentsData } = await supabase
      .from('memberships')
      .select('student_id, created_at')
      .eq('tenant_id', tenantId)
      .gte('created_at', currentRange.startDate.toISOString())
      .lte('created_at', currentRange.endDate.toISOString());

    // Buscar receita total para calcular LTV
    const { data: paymentsData } = await supabase
      .from('payments')
      .select('amount, student_id')
      .eq('tenant_id', tenantId)
      .eq('status', 'paid');

    // Calcular métricas baseadas em alunos com memberships
    const totalStudentsWithMemberships = new Set((allMembershipsData || []).map(m => m.student_id)).size;
    const activeStudents = (activeStudentsData || []).length;
    const newStudents = (newStudentsData || []).length;
    const churnedStudents = (canceledMembershipsData || []).length;

    // Calcular taxa de retenção e churn baseado em alunos com memberships
    const retentionRate = totalStudentsWithMemberships > 0 ? (activeStudents / totalStudentsWithMemberships) * 100 : 0;
    const churnRate = totalStudentsWithMemberships > 0 ? (churnedStudents / totalStudentsWithMemberships) * 100 : 0;

    // Calcular LTV médio (receita total / número de alunos únicos)
    const totalRevenue = (paymentsData || []).reduce((sum, payment) => sum + Number(payment.amount), 0);
    const uniquePayingStudents = new Set((paymentsData || []).map(p => p.student_id)).size;
    const averageLifetimeValue = uniquePayingStudents > 0 ? totalRevenue / uniquePayingStudents : 0;

    const metrics: StudentMetrics = {
      totalStudents: totalStudentsWithMemberships,
      activeStudents,
      newStudents,
      churnedStudents,
      retentionRate,
      churnRate,
      averageLifetimeValue
    };

    return {
      success: true,
      data: metrics,
      message: 'Métricas de alunos obtidas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

/**
 * Busca métricas de alunos com comparação de períodos
 */
export async function getStudentMetricsWithComparison(
  currentRange: DateRange,
  previousRange: DateRange
): Promise<DashboardActionResult<{
  current: StudentMetrics;
  previous: StudentMetrics;
}>> {
  try {
    const [currentResult, previousResult] = await Promise.all([
      getStudentMetrics(currentRange),
      getStudentMetrics(previousRange)
    ]);

    if (!currentResult.success) {
      return { success: false, error: currentResult.error };
    }

    if (!previousResult.success) {
      return { success: false, error: previousResult.error };
    }

    return {
      success: true,
      data: {
        current: currentResult.data!,
        previous: previousResult.data!
      },
      message: 'Métricas de alunos com comparação obtidas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}

/**
 * Busca métricas históricas de alunos formatadas para KPIs
 */
export async function getStudentKPIMetrics(): Promise<DashboardActionResult<{
  retentionRate: MetricWithGrowth;
  churnRate: MetricWithGrowth;
  averageLifetimeValue: MetricWithGrowth;
}>> {
  try {
    // Definir períodos de comparação (últimos 30 dias vs 30 dias anteriores)
    const now = new Date();
    const currentPeriodStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const previousPeriodStart = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
    const previousPeriodEnd = currentPeriodStart;

    const currentRange: DateRange = {
      startDate: currentPeriodStart,
      endDate: now,
      period: 'custom',
      label: 'Últimos 30 dias'
    };

    const previousRange: DateRange = {
      startDate: previousPeriodStart,
      endDate: previousPeriodEnd,
      period: 'custom',
      label: '30 dias anteriores'
    };

    // Buscar métricas com comparação
    const result = await getStudentMetricsWithComparison(currentRange, previousRange);

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || 'Erro ao buscar métricas de alunos'
      };
    }

    const { current, previous } = result.data;

    // Criar métricas formatadas
    const retentionRate = createMetricWithGrowth(
      current.retentionRate,
      previous.retentionRate,
      false, // não é moeda
      false  // não é valor absoluto
    );

    const churnRate = createMetricWithGrowth(
      current.churnRate,
      previous.churnRate,
      false, // não é moeda
      false  // não é valor absoluto
    );

    const averageLifetimeValue = createMetricWithGrowth(
      current.averageLifetimeValue,
      previous.averageLifetimeValue,
      true,  // é moeda
      true   // é valor absoluto
    );

    // Ajustar formatação para percentuais
    retentionRate.formatted = {
      current: formatPercentage(current.retentionRate),
      previous: formatPercentage(previous.retentionRate),
      growth: retentionRate.formatted.growth
    };

    churnRate.formatted = {
      current: formatPercentage(current.churnRate),
      previous: formatPercentage(previous.churnRate),
      growth: churnRate.formatted.growth
    };

    return {
      success: true,
      data: {
        retentionRate,
        churnRate,
        averageLifetimeValue
      },
      message: 'Métricas KPI de alunos obtidas com sucesso'
    };

  } catch (error) {
    return {
      success: false,
      error: `Erro interno: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
}
